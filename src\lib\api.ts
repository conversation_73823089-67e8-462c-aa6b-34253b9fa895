import {
  ApiResponse,
  LoginCredentials,
  AuthToken,
  Product,
  ProductGroup,
  UserGroup,
  StockInfo,
  StockUpdate,
  ProductImage,
  ProductUserPrice,
  DisallowedUserGroup,
  PurchaseAvailability,
  Operator,
  InventoryCountConfig,
  InventoryCountItem,
  InventoryCountSession,
  InventoryCountFilters,
  InventoryCountConfigProduct,
  InventoryCountRecord,
  StockNotificationRule,
  StockNotificationRecord,
  StockNotificationSettings
} from './types';
import { MockInventoryAPI } from './mock-inventory-api';

const API_BASE_URL = 'http://gizmodelphine.ddns.net:8081/api/v2.0';

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public data?: unknown
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class ApiService {
  private static getAuthHeaders(): Record<string, string> {
    if (typeof window === 'undefined') {
      console.log('服务器端渲染，跳过认证头部');
      return {};
    }

    const token = localStorage.getItem('token');
    console.log('获取到的Token:', token ? '存在' : '不存在');

    if (!token) {
      console.error('未找到认证令牌，请先登录。');
      throw new Error('未找到认证令牌，请先登录。');
    }

    const headers = {
      'accept': 'application/json',
      'Authorization': token
    };

    console.log('设置的认证头部:', headers);
    return headers;
  }

  private static async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    console.log('API请求URL:', url);

    const fetchOptions: RequestInit = {
      ...options,
      headers: {
        ...this.getAuthHeaders(),
        ...options.headers,
      }
    };

    console.log('完整的请求选项:', fetchOptions);

    try {
      const response = await fetch(url, fetchOptions);
      console.log('API响应状态:', response.status, response.statusText);

      if (!response.ok) {
        if (response.status === 401) {
          console.error('认证失败，状态码:', response.status);
          if (typeof window !== 'undefined') {
            localStorage.removeItem('token');
            window.location.href = '/login';
          }
          throw new ApiError('认证失败', 401);
        }

        let errorData = null;
        let errorText = '';
        try {
          const text = await response.text();
          errorText = text;
          if (text) {
            errorData = JSON.parse(text);
          }
        } catch (parseError) {
          console.warn('无法解析错误响应JSON:', errorText);
          errorData = { rawError: errorText };
        }

        const isStockError = response.status === 400 &&
          (url.includes('/productsstock/') ||
           (errorData?.errorCodeReadable?.includes('Stock')));

        if (!isStockError) {
          console.error('=== API错误详情 ===');
          console.error('URL:', url);
          console.error('状态码:', response.status);
          console.error('状态文本:', response.statusText);
          console.error('错误数据:', errorData);
          console.error('原始错误文本:', errorText);
          console.error('请求选项:', {
            method: fetchOptions.method || 'GET',
            headers: fetchOptions.headers,
            body: fetchOptions.body
          });
          console.error('==================');
        }

        // 构建更详细的错误信息
        let errorMessage = `HTTP 错误! 状态: ${response.status}`;
        if (errorData) {
          // 处理特定的业务错误
          if (errorData.errorCodeTypeReadable === 'NonUniqueEntityValue') {
            // 提取重复的字段名称和值
            const message = errorData.message || '';
            const match = message.match(/property : (\w+) value : (.*?) is not unique/);
            if (match) {
              const field = match[1];
              const value = match[2];
              if (field === 'Name') {
                errorMessage = `产品名称 "${value}" 已存在，请使用不同的名称`;
              } else {
                errorMessage = `${field} "${value}" 已存在，请使用不同的值`;
              }
            } else {
              errorMessage = '数据不唯一，请检查输入';
            }
          }
          else if (errorData.message) {
            errorMessage = errorData.message;
          }

          if (errorData.errorCodeReadable && errorData.errorCodeTypeReadable !== 'NonUniqueEntityValue') {
            errorMessage += ` (${errorData.errorCodeReadable})`;
          }
          if (errorData.errors && Array.isArray(errorData.errors) && errorData.errors.length > 0) {
            errorMessage += ` - 详细错误: ${errorData.errors.join(', ')}`;
          }
        }

        throw new ApiError(
          errorMessage,
          response.status,
          errorData
        );
      }

      const data = await response.json();
      console.log('API响应数据:', data);
      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      const isStockError = url.includes('/productsstock/') &&
        error instanceof Error &&
        error.message.includes('400');

      if (!isStockError) {
        console.error(`API 请求失败: ${url}`, error);
      }

      throw error;
    }
  }

  // 认证相关
  static async login(credentials: LoginCredentials): Promise<ApiResponse<AuthToken>> {
    const endpoint = `/auth/accesstoken?Username=${encodeURIComponent(credentials.username)}&Password=${encodeURIComponent(credentials.password)}`;
    const url = `${API_BASE_URL}${endpoint}`;

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new ApiError(`HTTP 错误! 状态: ${response.status}`, response.status);
      }
      return await response.json();
    } catch (error) {
      console.error('登录 API 请求失败:', error);
      throw error;
    }
  }

  // 产品组相关 - 使用真实API
  static async fetchProductGroups(): Promise<ApiResponse<ProductGroup[]>> {
    console.log('使用真实API：获取产品组列表');
    return this.request('/productgroups?Pagination.Limit=1000');
  }

  // 产品相关（数据源切换为 Supabase）。注意：Supabase 单次最大返回 1000 条，因此分批拉取拼接
  static async fetchAllProducts(): Promise<ApiResponse<Product[]>> {
    const all: Product[] = [];
    const limit = 1000; // Supabase 默认单次上限
    let offset = 0;
    let total = Infinity;

    while (offset < total) {
      const res = await fetch(`/api/local/products?limit=${limit}&offset=${offset}`, { cache: 'no-store' });
      const data = await res.json();
      const items: Product[] = data?.items || [];
      const count: number = Number(data?.count ?? 0);
      total = isFinite(count) && count > 0 ? count : Infinity;
      all.push(...items);
      if (items.length < limit) break; // 最后一页
      offset += limit;
      // 安全阈值，避免异常循环
      if (offset > 200000) break;
    }

    return { result: all, httpStatusCode: 200, isError: false } as ApiResponse<Product[]>;
  }


  static async getProductById(id: number): Promise<ApiResponse<Product>> {
    const res = await fetch(`/api/local/products/${id}`, { cache: 'no-store' });
    const data = await res.json();
    return { result: data?.item || null, httpStatusCode: data?.ok ? 200 : 404, isError: !data?.ok } as ApiResponse<Product>;
  }

  // 分页搜索产品
  static async searchProducts(params: {
    search?: string;
    productGroupId?: number;
    limit?: number;
    offset?: number;
    sortBy?: string;
    inStockOnly?: boolean;
  }): Promise<ApiResponse<Product[]>> {
    const searchParams = new URLSearchParams();
    if (params.limit) searchParams.append('limit', String(params.limit));
    if (params.offset) searchParams.append('offset', String(params.offset));
    if (params.search) searchParams.append('search', params.search);
    if (params.productGroupId) searchParams.append('productGroupId', String(params.productGroupId));
    if (params.sortBy) searchParams.append('sortBy', params.sortBy);
    if (params.inStockOnly) searchParams.append('inStockOnly', 'true');

    const res = await fetch(`/api/local/products?${searchParams.toString()}`, { cache: 'no-store' });
    const data = await res.json();
    return { result: data?.items || [], httpStatusCode: 200, isError: false } as ApiResponse<Product[]>;
  }

  // 获取产品总数（用于分页）
  static async getProductsCount(params: {
    search?: string;
    productGroupId?: number;
    inStockOnly?: boolean;
  }): Promise<ApiResponse<{ count: number }>> {
    const searchParams = new URLSearchParams();
    if (params.search) searchParams.append('search', params.search);
    if (params.productGroupId) searchParams.append('productGroupId', String(params.productGroupId));
    if (params.inStockOnly) searchParams.append('inStockOnly', 'true');

    const res = await fetch(`/api/local/products?${searchParams.toString()}`, { cache: 'no-store' });
    const data = await res.json();
    return { result: { count: data?.count ?? 0 }, httpStatusCode: 200, isError: false } as ApiResponse<{ count: number }>;
  }

  // 操作员相关 - 使用真实API
  static async fetchOperators(): Promise<ApiResponse<Operator[]>> {
    console.log('使用真实API：获取操作员列表');
    return this.request('/operators');
  }

  static async getCurrentOperator(): Promise<ApiResponse<Operator>> {
    console.log('使用真实API：获取当前操作员');
    return this.request('/operators/current');
  }

  // 库存盘点配置相关 - 使用模拟数据
  static async getInventoryCountConfig(): Promise<ApiResponse<InventoryCountConfig>> {
    console.log('使用模拟数据：获取盘点配置');
    return MockInventoryAPI.getInventoryCountConfig();
  }

  static async saveInventoryCountConfig(config: InventoryCountConfig): Promise<ApiResponse<InventoryCountConfig>> {
    console.log('使用模拟数据：保存盘点配置', config);
    return MockInventoryAPI.saveInventoryCountConfig(config);
  }

  // 盘点配置产品相关
  static async getInventoryCountConfigProducts(configId: number): Promise<ApiResponse<InventoryCountConfigProduct[]>> {
    console.log('使用模拟数据：获取配置产品', configId);
    return MockInventoryAPI.getInventoryCountConfigProducts(configId);
  }

  static async saveInventoryCountConfigProducts(products: InventoryCountConfigProduct[]): Promise<ApiResponse<InventoryCountConfigProduct[]>> {
    console.log('使用模拟数据：保存配置产品', products);
    return MockInventoryAPI.saveInventoryCountConfigProducts(products);
  }

  // 库存盘点会话相关 - 使用模拟数据
  static async createInventoryCountSession(): Promise<ApiResponse<InventoryCountSession>> {
    console.log('使用模拟数据：创建盘点会话');
    return MockInventoryAPI.createInventoryCountSession();
  }

  static async getInventoryCountSession(sessionId: string): Promise<ApiResponse<InventoryCountSession>> {
    console.log('使用模拟数据：获取盘点会话', sessionId);
    // 暂时返回空，因为模拟API中没有实现这个方法
    return { result: null, httpStatusCode: 404, message: 'Not implemented in mock', isError: true };
  }

  static async getActiveInventoryCountSession(): Promise<ApiResponse<InventoryCountSession | null>> {
    console.log('使用模拟数据：获取活跃盘点会话');
    return MockInventoryAPI.getActiveInventoryCountSession();
  }

  // 库存盘点项目相关 - 使用模拟数据
  static async getInventoryCountItems(sessionId: string, productGroupId?: number): Promise<ApiResponse<InventoryCountItem[]>> {
    console.log('使用模拟数据：获取盘点项目', { sessionId, productGroupId });
    return MockInventoryAPI.getInventoryCountItems(sessionId, productGroupId);
  }

  static async saveInventoryCountItem(item: Partial<InventoryCountItem>): Promise<ApiResponse<InventoryCountItem>> {
    console.log('使用模拟数据：保存盘点项目', item);
    return MockInventoryAPI.saveInventoryCountItem(item);
  }

  static async submitInventoryCountSession(sessionId: string): Promise<ApiResponse<InventoryCountSession>> {
    console.log('使用模拟数据：提交盘点会话', sessionId);
    return MockInventoryAPI.submitInventoryCountSession(sessionId);
  }

  // 库存盘点历史记录相关 - 使用模拟数据
  static async getInventoryCountHistory(filters: InventoryCountFilters): Promise<ApiResponse<InventoryCountItem[]>> {
    console.log('使用模拟数据：获取盘点历史', filters);
    return MockInventoryAPI.getInventoryCountHistory(filters);
  }

  static async getInventoryCountSessions(filters?: {
    dateFrom?: string;
    dateTo?: string;
    operatorId?: number;
    status?: string;
  }): Promise<ApiResponse<InventoryCountSession[]>> {
    const searchParams = new URLSearchParams();

    if (filters?.dateFrom) {
      searchParams.append('dateFrom', filters.dateFrom);
    }

    if (filters?.dateTo) {
      searchParams.append('dateTo', filters.dateTo);
    }

    if (filters?.operatorId) {
      searchParams.append('operatorId', filters.operatorId.toString());
    }

    if (filters?.status) {
      searchParams.append('status', filters.status);
    }

    const params = searchParams.toString();
    return this.request(`/inventory-count/sessions${params ? '?' + params : ''}`);
  }

  static async exportInventoryCountReport(filters: InventoryCountFilters): Promise<Blob> {
    console.log('使用模拟数据：导出盘点报告', filters);
    return MockInventoryAPI.exportInventoryCountReport(filters);
  }

  static async updateProduct(productData: Product): Promise<ApiResponse> {
    return this.request('/products', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(productData)
    });
  }

  static async createProduct(productData: Omit<Product, 'id'>): Promise<ApiResponse<Product>> {
    return this.request('/products', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(productData)
    });
  }

  // 完整的产品创建API (GENERAL API)
  static async createProductComplete(productData: {
    productType?: number;
    productGroupId?: number;
    name: string;
    description?: string;
    price: number;
    cost?: number;
    disallowClientOrder?: boolean;
    restrictGuestSale?: boolean;
    restrictSale?: boolean;
    purchaseOptions?: number;
    points?: number;
    pointsPrice?: number;
    barcode?: string;
    enableStock?: boolean;
    disallowSaleIfOutOfStock?: boolean;
    stockAlert?: boolean;
    stockAlertThreshold?: number;
    stockTargetDifferentProduct?: boolean;
    stockTargetProductId?: number;
    stockProductAmount?: number;
    isDeleted?: boolean;
    isService?: boolean;
    displayOrder?: number;
  }): Promise<ApiResponse<Product>> {
    // 创建最小化的产品数据，只包含必需字段和有意义的值
    const minimalData: any = {
      name: productData.name.trim(),
      price: productData.price || 0,
    };

    // 只有在非空/非零/非默认值时才添加字段
    if (productData.description?.trim()) {
      minimalData.description = productData.description.trim();
    }

    if (productData.productGroupId && productData.productGroupId > 0) {
      minimalData.productGroupId = productData.productGroupId;
    }

    if (productData.cost && productData.cost > 0) {
      minimalData.cost = productData.cost;
    }

    if (productData.barcode?.trim()) {
      minimalData.barcode = productData.barcode.trim();
    }

    if (productData.points && productData.points > 0) {
      minimalData.points = productData.points;
    }

    if (productData.pointsPrice && productData.pointsPrice > 0) {
      minimalData.pointsPrice = productData.pointsPrice;
    }

    // 布尔值字段 - 只在非默认值时发送
    if (productData.disallowClientOrder === true) {
      minimalData.disallowClientOrder = true;
    }

    if (productData.restrictGuestSale === true) {
      minimalData.restrictGuestSale = true;
    }

    if (productData.restrictSale === true) {
      minimalData.restrictSale = true;
    }

    if (productData.isService === true) {
      minimalData.isService = true;
    }

    if (productData.isDeleted === true) {
      minimalData.isDeleted = true;
    }

    // 库存相关 - 只在启用库存时发送
    if (productData.enableStock !== undefined) {
      minimalData.enableStock = productData.enableStock;

      if (productData.enableStock && productData.disallowSaleIfOutOfStock !== undefined) {
        minimalData.disallowSaleIfOutOfStock = productData.disallowSaleIfOutOfStock;
      }

      if (productData.enableStock && productData.stockAlert !== undefined) {
        minimalData.stockAlert = productData.stockAlert;

        if (productData.stockAlert && productData.stockAlertThreshold && productData.stockAlertThreshold > 0) {
          minimalData.stockAlertThreshold = productData.stockAlertThreshold;
        }
      }
    }

    // 购买选项
    if (productData.purchaseOptions !== undefined && productData.purchaseOptions >= 0) {
      minimalData.purchaseOptions = productData.purchaseOptions;
    }

    // 显示顺序
    if (productData.displayOrder && productData.displayOrder > 0) {
      minimalData.displayOrder = productData.displayOrder;
    }

    console.log('发送到API的最小化产品数据:', minimalData);

    return this.request('/products', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(minimalData)
    });
  }

  // 库存相关
  static async fetchProductStock(productId: number): Promise<StockInfo | string> {
    try {
      const data = await this.request<ApiResponse<StockInfo>>(`/productsstock/${productId}`);
      if (data.result?.onHand !== undefined) {
        return data.result;
      }
      return '未知';
    } catch (error) {
      if (error instanceof ApiError) {
        const errorDetails = error.data?.errorCodeReadable || error.data?.message || '';

        if (errorDetails.includes('StockDisabled') ||
            errorDetails.includes('StockException')) {
          return '库存已禁用';
        }
      }
      return '不可用';
    }
  }

  static async updateProductStock(stockUpdate: StockUpdate): Promise<ApiResponse> {
    return this.request('/productsstock', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(stockUpdate)
    });
  }

  // 图片相关
  static async uploadProductImage(productId: number, imageBase64: string): Promise<ApiResponse<ProductImage>> {
    return this.request(`/products/${productId}/images`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ image: imageBase64 })
    });
  }

  static async deleteProductImage(productId: number, productImageId: number): Promise<ApiResponse> {
    return this.request(`/products/${productId}/images/${productImageId}`, {
      method: 'DELETE'
    });
  }

  static async updateProductImage(imageData: ProductImage): Promise<ApiResponse> {
    return this.request('/products/images', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(imageData)
    });
  }

  // 用户组相关
  static async fetchUserGroups(): Promise<ApiResponse<UserGroup[]>> {
    return this.request('/usergroups?Pagination.Limit=1000');
  }

  // 用户价格相关
  static async fetchProductUserPrices(productId: number): Promise<ApiResponse<ProductUserPrice[]>> {
    return this.request(`/products/${productId}/userprices`);
  }

  static async createProductUserPrice(productId: number, userPriceData: {
    userGroupId: number;
    price: number;
    pointsPrice?: number;
    purchaseOptions?: number;
    isEnabled?: boolean;
  }): Promise<ApiResponse<ProductUserPrice>> {
    return this.request(`/products/${productId}/userprices`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userPriceData)
    });
  }

  static async updateProductUserPrice(userPriceData: ProductUserPrice): Promise<ApiResponse> {
    return this.request('/products/userprices', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userPriceData)
    });
  }

  static async deleteProductUserPrice(productId: number, userPriceId: number): Promise<ApiResponse> {
    return this.request(`/products/${productId}/userprices/${userPriceId}`, {
      method: 'DELETE'
    });
  }

  // 禁用用户组相关
  static async fetchProductDisallowedUserGroups(productId: number): Promise<ApiResponse<DisallowedUserGroup[]>> {
    return this.request(`/products/${productId}/disallowedusergroups`);
  }

  static async createDisallowedUserGroup(productId: number, disallowedGroupData: Omit<DisallowedUserGroup, 'id'>): Promise<ApiResponse<DisallowedUserGroup>> {
    return this.request(`/products/${productId}/disallowedusergroups`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(disallowedGroupData)
    });
  }

  static async updateDisallowedUserGroup(disallowedGroupData: {
    id: number;
    productId: number;
    userGroupId: number;
    isDisallowed: boolean;
  }): Promise<ApiResponse> {
    return this.request('/products/disallowedusergroups', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(disallowedGroupData)
    });
  }

  static async deleteDisallowedUserGroup(productId: number, disallowedGroupId: number): Promise<ApiResponse> {
    return this.request(`/products/${productId}/disallowedusergroups/${disallowedGroupId}`, {
      method: 'DELETE'
    });
  }

  // 购买可用性相关
  static async updateProductPurchaseAvailability(productId: number, availabilityData: PurchaseAvailability): Promise<ApiResponse> {
    return this.request(`/products/${productId}/purchaseavailability`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(availabilityData)
    });
  }

  // 库存通知相关
  static async getStockNotificationRules(filters?: {
    productGroupId?: number;
    isEnabled?: boolean;
  }): Promise<ApiResponse<StockNotificationRule[]>> {
    const searchParams = new URLSearchParams();

    if (filters?.productGroupId) {
      searchParams.append('ProductGroupId', filters.productGroupId.toString());
    }

    if (filters?.isEnabled !== undefined) {
      searchParams.append('IsEnabled', filters.isEnabled.toString());
    }

    const params = searchParams.toString();
    return this.request(`/stock-notifications/rules${params ? '?' + params : ''}`);
  }

  static async createStockNotificationRule(ruleData: Omit<StockNotificationRule, 'id'>): Promise<ApiResponse<StockNotificationRule>> {
    return this.request('/stock-notifications/rules', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(ruleData)
    });
  }

  static async updateStockNotificationRule(ruleData: StockNotificationRule): Promise<ApiResponse<StockNotificationRule>> {
    return this.request(`/stock-notifications/rules/${ruleData.id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(ruleData)
    });
  }

  static async deleteStockNotificationRule(ruleId: number): Promise<ApiResponse> {
    return this.request(`/stock-notifications/rules/${ruleId}`, {
      method: 'DELETE'
    });
  }

  static async getStockNotificationRecords(filters?: {
    dateFrom?: string;
    dateTo?: string;
    productGroupId?: number;
    productId?: number;
    notificationLevel?: 'low' | 'critical' | 'out_of_stock';
    isRead?: boolean;
    isResolved?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse<StockNotificationRecord[]>> {
    const searchParams = new URLSearchParams();

    // 分页参数
    searchParams.append('Pagination.Limit', (filters?.limit || 50).toString());
    searchParams.append('Pagination.Offset', (filters?.offset || 0).toString());

    if (filters?.dateFrom) {
      searchParams.append('DateFrom', filters.dateFrom);
    }

    if (filters?.dateTo) {
      searchParams.append('DateTo', filters.dateTo);
    }

    if (filters?.productGroupId) {
      searchParams.append('ProductGroupId', filters.productGroupId.toString());
    }

    if (filters?.productId) {
      searchParams.append('ProductId', filters.productId.toString());
    }

    if (filters?.notificationLevel) {
      searchParams.append('NotificationLevel', filters.notificationLevel);
    }

    if (filters?.isRead !== undefined) {
      searchParams.append('IsRead', filters.isRead.toString());
    }

    if (filters?.isResolved !== undefined) {
      searchParams.append('IsResolved', filters.isResolved.toString());
    }

    return this.request(`/stock-notifications/records?${searchParams.toString()}`);
  }

  static async markNotificationAsRead(recordId: number): Promise<ApiResponse> {
    return this.request(`/stock-notifications/records/${recordId}/read`, {
      method: 'PUT'
    });
  }

  static async markNotificationAsResolved(recordId: number, notes?: string): Promise<ApiResponse> {
    return this.request(`/stock-notifications/records/${recordId}/resolve`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ notes })
    });
  }

  static async getStockNotificationSettings(): Promise<ApiResponse<StockNotificationSettings>> {
    return this.request('/stock-notifications/settings');
  }

  static async updateStockNotificationSettings(settings: StockNotificationSettings): Promise<ApiResponse<StockNotificationSettings>> {
    return this.request('/stock-notifications/settings', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(settings)
    });
  }

  // 手动触发库存检查（用于测试）
  static async triggerStockCheck(): Promise<ApiResponse> {
    return this.request('/stock-notifications/check', {
      method: 'POST'
    });
  }
}