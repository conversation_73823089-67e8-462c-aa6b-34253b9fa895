import axios from 'axios';

const BASE_URL = process.env.BOXHERO_BASE_URL || 'https://rest.boxhero-app.com';
const API_TOKEN = process.env.BOXHERO_API_TOKEN;
const LIMIT = Number(process.env.BOXHERO_ITEMS_LIMIT || 100);

function sleep(ms) {
  return new Promise((res) => setTimeout(res, ms));
}

const http = axios.create({
  baseURL: BASE_URL,
  timeout: 30000,
  headers: {
    Accept: 'application/json',
    Authorization: `Bearer ${API_TOKEN}`,
  },
});

// 带速率限制处理与重试的 GET 请求
async function getWithRateLimit(url, config = {}, attempt = 0) {
  try {
    const res = await http.get(url, config);
    return res;
  } catch (err) {
    const res = err.response;
    // 429 或 too many requests 类型
    const status = res?.status;
    const type = res?.data?.type;
    if (status === 429 || type === '/errors/too-many-requests') {
      // 读取 X-Ratelimit-Reset 头（秒）
      const resetSec = Number(res.headers?.['x-ratelimit-reset'] || 1);
      const waitMs = Math.max(0, resetSec) * 1000;
      console.warn(`Rate limited. 等待 ${waitMs} ms 后重试...`);
      await sleep(waitMs);
      return getWithRateLimit(url, config, attempt + 1);
    }
    // 5xx 简单指数退避重试最多 3 次
    if (status >= 500 && attempt < 3) {
      const backoff = Math.min(1000 * 2 ** attempt, 8000);
      console.warn(`服务器错误 ${status}，${backoff}ms 后重试 (attempt=${attempt + 1})`);
      await sleep(backoff);
      return getWithRateLimit(url, config, attempt + 1);
    }
    throw err;
  }
}

// 获取所有 items（分页，直到 has_more=false）
export async function fetchAllItems({ locationIds = [], limit = LIMIT } = {}) {
  if (!API_TOKEN) throw new Error('缺少 BOXHERO_API_TOKEN');
  const params = new URLSearchParams();
  params.set('limit', String(limit));
  // location_ids 支持多次出现
  (locationIds || [])
    .filter(Boolean)
    .forEach((id) => params.append('location_ids', String(id)));

  let all = [];
  let cursor = undefined;
  let page = 0;
  while (true) {
    const query = new URLSearchParams(params);
    if (cursor) query.set('cursor', cursor);
    const url = `/v1/items?${query.toString()}`;
    const res = await getWithRateLimit(url);
    const data = res.data;
    // 兼容字段名称
    const items = data?.items || data?.data || [];
    const hasMore = data?.has_more ?? data?.hasMore ?? false;
    cursor = data?.cursor || data?.next_cursor || undefined;

    // 抽取 id, name, barcode, sku, quantity
    for (const it of items) {
      all.push({
        id: it?.id ?? it?.item_id ?? null,
        name: it?.name ?? null,
        barcode: it?.barcode ?? it?.bar_code ?? it?.sku ?? null,
        sku: it?.sku ?? null,
        quantity: it?.quantity ?? it?.qty ?? 0,
      });
    }

    page += 1;
    console.log(`已获取第 ${page} 页，累计 ${all.length} 条`);

    if (!hasMore) break;
    // 避免超过 5 QPS，保险起见每页之间稍作延时
    await sleep(250);
  }
  return all;
}

