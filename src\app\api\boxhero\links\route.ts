export const runtime = 'nodejs';
import { NextRequest } from 'next/server';
import { getLinkedBarcodesByProductIds } from '@/dbClient';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json().catch(() => ({}));
    const productIds: number[] = Array.isArray(body?.productIds) ? body.productIds : [];
    const data = await getLinkedBarcodesByProductIds(productIds);
    return new Response(JSON.stringify({ ok: true, data }), {
      status: 200,
      headers: { 'content-type': 'application/json' },
    });
  } catch (e: any) {
    console.error('获取 BoxHero 关联失败:', e);
    return new Response(JSON.stringify({ ok: false, error: e?.message || 'internal error' }), {
      status: 500,
      headers: { 'content-type': 'application/json' },
    });
  }
}

