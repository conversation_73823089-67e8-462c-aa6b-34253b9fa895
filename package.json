{"name": "wms-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "boxhero:sync:once": "node -e \"import('./src/index.js').then(m=>m.runOnce&&m.runOnce())\""}, "dependencies": {"@supabase/supabase-js": "^2.55.0", "axios": "^1.7.7", "clsx": "^2.1.1", "next": "15.4.5", "pg": "^8.12.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}