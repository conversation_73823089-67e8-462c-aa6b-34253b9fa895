'use client';

import { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ApiService } from '@/lib/api';
import { ProductGroup, type ProductFilters } from '@/lib/types';
import { debounce } from '@/lib/utils';

interface ProductFiltersProps {
  onFiltersChange?: (filters: ProductFilters) => void;
  onProductGroupSelect?: (productGroupId: number | undefined) => void;
}

export function ProductFilters({ onFiltersChange, onProductGroupSelect }: ProductFiltersProps) {
  const [filters, setFilters] = useState<ProductFilters>({});
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [isLoadingGroups, setIsLoadingGroups] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [isSearching, setIsSearching] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  // 输入框本地状态（按 Enter 或点击按钮后再提交到 filters）
  const [searchInput, setSearchInput] = useState<string>('');

  // 提交搜索：由按钮或回车触发；也可保留轻微防抖
  const commitSearch = debounce((search: string) => {
    const trimmedSearch = search.trim() || undefined;
    setFilters(prev => ({ ...prev, search: trimmedSearch }));
    setIsSearching(false);
  }, 150);

  useEffect(() => {
    loadProductGroups();
  }, []);

  useEffect(() => {
    onFiltersChange?.(filters);
  }, [filters, onFiltersChange]);

  const loadProductGroups = async () => {
    try {
      setIsLoadingGroups(true);
      setDebugInfo('正在加载产品组...');
      
      console.log('开始加载产品组...');
      const response = await ApiService.fetchProductGroups();
      console.log('产品组API响应:', response);
      
      // 检查响应格式
      if (response && response.result) {
        console.log('产品组result内容:', response.result);
        
        // 尝试不同的数据访问方式
        let productGroupsData = null;
        const result = response.result as any;
        
        if (Array.isArray(result)) {
          productGroupsData = result;
        } else if (result.data && Array.isArray(result.data)) {
          productGroupsData = result.data;
        } else if (result.items && Array.isArray(result.items)) {
          productGroupsData = result.items;
        } else if (result.productGroups && Array.isArray(result.productGroups)) {
          productGroupsData = result.productGroups;
        } else {
          // 如果都不是数组，尝试将整个result作为单个对象处理
          console.log('尝试将result作为单个产品组处理');
          productGroupsData = [result];
        }
        
        if (productGroupsData && Array.isArray(productGroupsData)) {
          setProductGroups(productGroupsData);
          setDebugInfo(`成功加载 ${productGroupsData.length} 个产品组`);
          console.log('产品组数据:', productGroupsData);
        } else {
          setProductGroups([]);
          setDebugInfo('产品组数据格式错误 - 无法解析为数组');
          console.error('产品组数据格式错误 - 无法解析为数组:', response.result);
        }
      } else {
        setProductGroups([]);
        setDebugInfo('产品组响应格式错误');
        console.error('产品组响应格式错误:', response);
      }
    } catch (error) {
      console.error('加载产品组失败:', error);
      setProductGroups([]);
      setDebugInfo(`加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoadingGroups(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
    setSearchInput(val);
    // 输入阶段不触发后端搜索，只展示“输入中”状态
    if (searchTimeoutRef.current) clearTimeout(searchTimeoutRef.current);
    if (val.trim()) {
      setIsSearching(true);
      searchTimeoutRef.current = setTimeout(() => setIsSearching(false), 1500);
    } else {
      setIsSearching(false);
    }
  };

  const triggerSearch = () => {
    setIsSearching(true);
    commitSearch(searchInput);
  };

  const handleGroupChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const productGroupId = e.target.value ? Number(e.target.value) : undefined;
    setFilters(prev => ({ ...prev, productGroupId }));
    
    // 通知父组件产品组选择变化
    onProductGroupSelect?.(productGroupId);
  };

  const handleStockFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({ ...prev, inStockOnly: e.target.checked }));
  };

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilters(prev => ({ ...prev, sortBy: e.target.value || undefined }));
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* 调试信息 */}
      {debugInfo && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">{debugInfo}</p>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* 产品组选择 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            产品组
          </label>
          <select
            value={filters.productGroupId || ''}
            onChange={handleGroupChange}
            className="w-full h-10 rounded-lg border border-gray-300 px-3 py-2 text-sm text-black focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={isLoadingGroups}
          >
            <option value="" className="text-black">请选择产品组</option>
            {Array.isArray(productGroups) && productGroups.map(group => (
              <option key={group.id} value={group.id} className="text-black">
                {group.name}
              </option>
            ))}
          </select>
          {isLoadingGroups && (
            <p className="text-xs text-gray-500 mt-1">加载中...</p>
          )}
        </div>

        {/* 搜索框 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            搜索
            {isSearching && (
              <span className="ml-2 text-xs text-blue-600">
                <span className="animate-pulse">搜索中...</span>
              </span>
            )}
          </label>
          <div className="relative">
            <Input
              type="text"
              placeholder="按名称、条码搜索...（回车或点右侧按钮执行）"
              value={searchInput}
              onChange={handleSearchChange}
              onKeyDown={(e) => { if (e.key === 'Enter') triggerSearch(); }}
              className="w-full text-black pr-20"
            />
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
              {isSearching && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              )}
              <Button size="sm" className="px-3 py-1" onClick={triggerSearch}>搜索</Button>
            </div>
          </div>
        </div>

        {/* 库存筛选 */}
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="inStockFilter"
            checked={filters.inStockOnly || false}
            onChange={handleStockFilterChange}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="inStockFilter" className="text-sm font-medium text-gray-700">
            仅显示有库存
          </label>
        </div>

        {/* 排序 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            排序
          </label>
          <select
            value={filters.sortBy || ''}
            onChange={handleSortChange}
            className="w-full h-10 rounded-lg border border-gray-300 px-3 py-2 text-sm text-black focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="" className="text-black">默认排序</option>
            <option value="name_asc" className="text-black">产品名称 A→Z</option>
            <option value="name_desc" className="text-black">产品名称 Z→A</option>
            <option value="stock_desc" className="text-black">系统库存 多→少</option>
            <option value="stock_asc" className="text-black">系统库存 少→多</option>
            <option value="boxhero_desc" className="text-black">BoxHero库存 多→少</option>
            <option value="boxhero_asc" className="text-black">BoxHero库存 少→多</option>
            <option value="price_desc" className="text-black">价格 高→低</option>
            <option value="price_asc" className="text-black">价格 低→高</option>
            <option value="id_desc" className="text-black">ID 新→旧</option>
            <option value="id_asc" className="text-black">ID 旧→新</option>
          </select>
        </div>
      </div>
    </div>
  );
} 