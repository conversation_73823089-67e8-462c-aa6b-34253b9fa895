export const runtime = 'nodejs';
import { NextRequest } from 'next/server';
import { getTableColumns } from '@/dbClient';

export async function GET(_req: NextRequest, context: { params: { table: string } }) {
  try {
    const table = context.params.table;
    if (!table) {
      return new Response(JSON.stringify({ ok: false, error: 'table param required' }), { status: 400 });
    }
    const cols = await getTableColumns(table);
    return new Response(JSON.stringify({ ok: true, table, columns: cols }), { status: 200, headers: { 'content-type': 'application/json' } });
  } catch (e: any) {
    console.error('读取数据表结构失败:', e);
    return new Response(JSON.stringify({ ok: false, error: e?.message || 'internal error' }), { status: 500, headers: { 'content-type': 'application/json' } });
  }
}

