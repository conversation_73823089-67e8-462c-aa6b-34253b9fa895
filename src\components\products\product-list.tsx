'use client';
import { useEffect, useMemo, useState } from 'react';
import { ProductCard } from './product-card';
import { LoadingIndicator } from '@/components/ui/loading';
import { useProductSearch } from '@/hooks/use-product-search';
import { type ProductFilters } from '@/lib/types';

interface ProductListProps {
  selectedProductGroupId?: number | undefined;
  filters?: ProductFilters;
}

export function ProductList({ selectedProductGroupId, filters: externalFilters }: ProductListProps) {
  const {
    products,
    isLoading,
    error,
    totalCount,
    refresh,
  } = useProductSearch(externalFilters || {}, selectedProductGroupId, {
    debounceMs: 500,
  });

  const [boxheroMap, setBoxheroMap] = useState<Record<string, number>>({});

  // 批量加载当前产品列表的 BoxHero 数量（条码或 SKU），避免逐卡并发造成资源耗尽
  useEffect(() => {
    let aborted = false;
    async function loadBatch() {
      try {
        const barcodes = Array.from(new Set(products.map(p => p.barcode).filter(Boolean) as string[]));
        const skus = Array.from(new Set(products.map(p => p.sku).filter(Boolean) as string[]));
        if (barcodes.length === 0 && skus.length === 0) {
          setBoxheroMap({});
          return;
        }
        // 分批请求，避免请求体过大
        const chunk = <T,>(arr: T[], size: number) => arr.reduce<T[][]>((acc, _, i) => (i % size ? acc : [...acc, arr.slice(i, i + size)]), []);
        const barcodeChunks = chunk(barcodes, 200);
        const skuChunks = chunk(skus, 200);

        const results: Record<string, number> = {};
        for (let i = 0; i < Math.max(barcodeChunks.length, skuChunks.length); i++) {
          const payload: any = {};
          if (barcodeChunks[i]?.length) payload.barcodes = barcodeChunks[i];
          if (skuChunks[i]?.length) payload.skus = skuChunks[i];
          if (!payload.barcodes && !payload.skus) continue;
          const res = await fetch('/api/boxhero/quantities', {
            method: 'POST', headers: { 'content-type': 'application/json' }, body: JSON.stringify(payload)
          });
          const json = await res.json();
          Object.assign(results, json?.data || {});
          if (aborted) return;
        }
        setBoxheroMap(results);
      } catch (e) {
        console.warn('批量加载 BoxHero 数量失败:', e);
        if (!aborted) setBoxheroMap({});
      }
    }
    loadBatch();
    return () => { aborted = true; };
  }, [products]);

  const displayProducts = useMemo(() => {
    if (externalFilters?.sortBy === 'boxhero_desc' || externalFilters?.sortBy === 'boxhero_asc') {
      const arr = [...products];
      arr.sort((a, b) => {
        const aq = Number((boxheroMap[a.barcode || ''] ?? boxheroMap[a.sku || '']) || 0);
        const bq = Number((boxheroMap[b.barcode || ''] ?? boxheroMap[b.sku || '']) || 0);
        return externalFilters.sortBy === 'boxhero_desc' ? (bq - aq) : (aq - bq);
      });
      return arr;
    }
    return products;
  }, [products, externalFilters?.sortBy, boxheroMap]);

  if (externalFilters?.sortBy === 'boxhero_desc' || externalFilters?.sortBy === 'boxhero_asc') {
    const barcodes = products.map(p => p.barcode).filter(Boolean);
    // 这里简单在客户端发请求。也可放到服务端聚合。
    // 注意：避免在渲染期发请求，实际可移动到上层 useEffect 中缓存。为了简洁，这里仅示例 API 使用方式。
  }



  // 如果没有选择产品组且没有搜索条件，显示提示信息
  if (!selectedProductGroupId && !externalFilters?.search) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">📦</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">请选择产品组或输入搜索条件</h3>
        <p className="text-gray-500">请在上方选择产品组以查看相关产品，或输入搜索条件进行全局搜索</p>
      </div>
    );
  }

  // 初始加载状态
  if (isLoading && products.length === 0) {
    return (
      <div className="flex justify-center py-12">
        <LoadingIndicator text="正在搜索产品..." />
      </div>
    );
  }

  // 错误状态
  if (error && products.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">搜索失败: {error}</p>
        <button
          onClick={refresh}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          重试
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 搜索结果统计 */}
      {(externalFilters?.search || selectedProductGroupId) && (
        <div className="text-sm text-gray-600 px-1">
          找到 {totalCount} 个产品，已显示 {products.length} 个
        </div>
      )}

      {products.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📦</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到产品</h3>
          <p className="text-gray-500">请尝试调整搜索条件</p>
        </div>
      ) : (
        <div className="space-y-4">
          {displayProducts.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              onUpdate={refresh}
              boxheroMap={boxheroMap}
            />
          ))}
        </div>
      )}
    </div>
  );
}