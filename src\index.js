import 'dotenv/config';
import cron from 'node-cron';
import { runSync } from './syncService.js';
import { closePool } from './dbClient.js';

const schedule = process.env.CRON_SCHEDULE || '0 * * * *';

export async function runOnce() {
  try {
    await runSync();
  } catch (e) {
    console.error('同步失败：', e?.response?.data || e);
    process.exitCode = 1;
  } finally {
    await closePool();
  }
}

async function main() {
  if (process.env.RUN_ONCE === '1') {
    await runOnce();
    return;
  }

  console.log(`已启动定时任务：${schedule}（每小时执行一次）`);
  cron.schedule(schedule, async () => {
    console.log(`[${new Date().toISOString()}] 开始同步...`);
    try {
      await runSync();
    } catch (e) {
      console.error('同步失败：', e?.response?.data || e);
    }
  });
}

main();

