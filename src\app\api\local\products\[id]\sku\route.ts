export const runtime = 'nodejs';
import { NextRequest } from 'next/server';
import { supabaseServer } from '@/lib/supabase-server';
import { mapRowToProduct } from '@/dbClient';

export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const { id: idStr } = await params;
  const id = Number(idStr);
  if (!id || Number.isNaN(id)) {
    return new Response(JSON.stringify({ ok: false, error: 'invalid id' }), { status: 400 });
  }
  try {
    const body = await req.json().catch(() => ({}));
    const sku = typeof body?.sku === 'string' ? body.sku.trim() : null;

    const { error: upErr } = await supabaseServer
      .from('productbase')
      .update({ sku })
      .eq('productid', id);
    if (upErr) throw upErr;

    const { data, error } = await supabaseServer
      .from('productbase')
      .select('*')
      .eq('productid', id)
      .limit(1)
      .maybeSingle();
    if (error) throw error;
    const item = data ? mapRowToProduct(data) : null;
    return new Response(JSON.stringify({ ok: true, item }), { status: 200, headers: { 'content-type': 'application/json' } });
  } catch (e: any) {
    console.error('更新 SKU 失败:', e);
    return new Response(JSON.stringify({ ok: false, error: e?.message || 'internal error' }), { status: 500 });
  }
}

