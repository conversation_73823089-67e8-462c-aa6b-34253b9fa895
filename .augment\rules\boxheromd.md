---
type: "manual"
description: ">-"
---
# API

{% embed url="https://www.boxhero.io/en/blog/boxhero-open-api-integrate-inventory-data" %}



***

## **API Reference**

For detailed endpoint documentation, please visit:\
[https://rest.boxhero-app.com/docs/api](https://rest.boxhero-app.com/docs/api)



## Endpoint

All API requests should be directed to the following base URL:\
<mark style="color:blue;">https://rest.boxhero-app.com</mark>&#x20;



## **Authentication**

All API requests must include an <mark style="color:blue;">**Authorization**</mark> header with a <mark style="color:blue;">**Bearer token**</mark>:

```
Authorization: Bearer {API_TOKEN}
```

To obtain an API token:

1. Log into your BoxHero account on the Desktop (PC/Web).
2. Navigate to <mark style="color:blue;">**`Settings`**</mark> > <mark style="color:blue;">**`Integrations & API`**</mark>.&#x20;
3. Generate a new API token.



## **Making Requests**

Here's a sample API request to retrieve products.

```bash
curl -X 'GET' \
  'https://rest.boxhero-app.com/v1/items?location_ids=12345&location_ids=34567&limit=100' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer test-api-token-123'
```



## Rate Limits

To ensure system stability, we impose the following rate limits:

* 5 queries per second for each endpoint.

Once you are rate limited, you will see an error message with the following:

* `X-Ratelimit-Limit` - Maximum queries per minute
* `X-Ratelimit-Remaining` - Remaining queries
* `X-Ratelimit-Reset` - Time (in seconds) remaining until query count reset



***

## Responses

### Success

Responses with HTTP status codes in the 200 range indicate successful API processing.

### Error

Status codes in the 400 or 500 range indicate API request failures:

* 400 range: Client-side error from information provided with the request (e.g., missing required parameters).
* 500 range: Server-side error.

**Example Error Response:**

```json
{
  "id": "ex_ku3gij67k9xzc8ef6r5esyu5",
  "type": "/errors/tokens/invalid",
  "title": "Authentication token is invalid. Please provide a valid token.",
  "correlation_id": "rq_z4g7dh55ykol7v2cx6homkpd",
  "given": "invalid-token-123"
}
```

* **id :** A unique ID to identify the error.
* **type :** A code in the form of a URL that identifies the type of error.&#x20;
* **title :** Provide the contents of the error in human-readable form.
* **correlation\_id :** Points to the ID of the request associated with the error.
* **others :** Additional fields may be included to provide additional information, such as "_given_" in the example.

### Common Error Types

<table><thead><tr><th width="252">Type</th><th>Description</th></tr></thead><tbody><tr><td>/errors/not-found</td><td>Requested resource not found (e.g., item with a specific ID does not exist).</td></tr><tr><td>/errors/invalid-request</td><td>Invalid parameters in request.</td></tr><tr><td>/errors/invalid-team-mode</td><td>Requested query cannot be processed in current team mode (e.g., using the <em>location</em> lookup API in <em>Basic Mode</em>).</td></tr><tr><td>/errors/tokens/required</td><td>Missing API token.</td></tr><tr><td>/errors/tokens/invalid</td><td>Invalid API token (e.g., API token has expired).</td></tr><tr><td>/errors/too-many-requests</td><td>Exceeded the rate limit.</td></tr><tr><td>/errors/unhandled</td><td>Unresolved server-side error.</td></tr><tr><td>/errors/core/usage-limit-exceeded</td><td>Reached usage limit. Plan upgrade required.</td></tr></tbody></table>



## Pagination

For endpoints returning large datasets (e.g., item lists, transaction lists), the list view API limits the number of items returned in a single request through pagination. We use cursor-based pagination:

{% hint style="info" %}
&#x20;To determine if pagination is required, check whether pagination-related parameters are present in the request body of the endpoint in the API documentation.
{% endhint %}

#### **Pagination Parameters**

* `has_more` : A boolean that indicates whether more data exists beyond the current page.
* `cursor` : Provides the cursor value for retrieving the next page.

#### **Retrieving the Next Page**

* Check if `has_more` is `true`. It means there’s another page available.
* If `true`, send another request including `cursor={received cursor value}` in the parameters. This will return the subsequent data.
* Repeat until `has_more` is `false` to retrieve the full list.



***

## Support and Feedback

If you encounter issues or need additional API functionality, please contact our [support team](https://docs-en.boxhero.io/etc/contact). We welcome your feedback for API improvements and new feature requests.&#x20;
