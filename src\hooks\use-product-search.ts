'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { ApiService } from '@/lib/api';
import { Product, ProductFilters } from '@/lib/types';
import { stockCache } from '@/lib/stock-cache';

interface UseProductSearchOptions {
  pageSize?: number;
  enableInfiniteScroll?: boolean;
  debounceMs?: number;
}

interface SearchState {
  products: Product[];
  isLoading: boolean;
  error: string | null;
  totalCount: number;
}

export function useProductSearch(
  filters: ProductFilters = {},
  selectedProductGroupId?: number,
  options: UseProductSearchOptions = {}
) {
  const {
    pageSize = 20,
    debounceMs = 500
  } = options;

  const [state, setState] = useState<SearchState>({
    products: [],
    isLoading: false,
    error: null,
    totalCount: 0,
  });

  const [productGroups, setProductGroups] = useState<{[key: number]: string}>({});
  const abortControllerRef = useRef<AbortController | null>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastSearchParamsRef = useRef<string>('');

  // 加载产品组映射
  useEffect(() => {
    loadProductGroups();
  }, []);

  const loadProductGroups = async () => {
    try {
      const response = await ApiService.fetchProductGroups();
      if (response.result && Array.isArray(response.result)) {
        const groupsMap: {[key: number]: string} = {};
        response.result.forEach(group => {
          groupsMap[group.id] = group.name;
        });
        setProductGroups(groupsMap);
      }
    } catch (error) {
      console.error('加载产品组失败:', error);
    }
  };

  // 取消正在进行的请求
  const cancelCurrentRequest = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  // 过滤和排序所有产品数据
  const filterAndSortProducts = useCallback((
    productsData: Product[],
    searchFilters: ProductFilters,
    groupId?: number
  ): Product[] => {
    // 过滤掉已删除的产品
    let filteredProducts = productsData.filter(product => {
      // 检查多种可能的删除状态字段
      if (product.isDeleted === true) return false;
      if ((product as any).deleted === true) return false;
      if ((product as any).status === 'deleted' || (product as any).status === 'inactive') return false;
      return true;
    });

    // 应用搜索过滤
    if (searchFilters.search) {
      const searchLower = searchFilters.search.toLowerCase();
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(searchLower) ||
        product.barcode?.toLowerCase().includes(searchLower) ||
        (product.productGroupId && productGroups[product.productGroupId]?.toLowerCase().includes(searchLower))
      );
    }

    // 应用产品组过滤
    if (groupId) {
      filteredProducts = filteredProducts.filter(product =>
        product.productGroupId === groupId
      );
    }

    // 应用库存过滤
    if (searchFilters.inStockOnly) {
      filteredProducts = filteredProducts.filter(product =>
        typeof (product as any).stock === 'number' && (product as any).stock > 0
      );
    }

    // 应用排序
    if (searchFilters.sortBy) {
      filteredProducts.sort((a, b) => {
        switch (searchFilters.sortBy) {
          case 'name_asc':
            return a.name.localeCompare(b.name);
          case 'name_desc':
            return b.name.localeCompare(a.name);
          case 'price_desc':
            return b.price - a.price;
          case 'price_asc':
            return a.price - b.price;
          case 'stock_desc': {
            // 系统库存 多→少
            const aCachedStock = stockCache.getCachedStock(a.id);
            const bCachedStock = stockCache.getCachedStock(b.id);
            const aStock = typeof aCachedStock === 'number' ? aCachedStock : 0;
            const bStock = typeof bCachedStock === 'number' ? bCachedStock : 0;
            return bStock - aStock;
          }
          case 'stock_asc': {
            // 系统库存 少→多
            const aCachedStock = stockCache.getCachedStock(a.id);
            const bCachedStock = stockCache.getCachedStock(b.id);
            const aStock = typeof aCachedStock === 'number' ? aCachedStock : 0;
            const bStock = typeof bCachedStock === 'number' ? bCachedStock : 0;
            return aStock - bStock;
          }
          case 'boxhero_desc': {
            // BoxHero库存 多→少（前端直接按卡片展示前，一般需要批量查询映射）
            return 0; // 这里先占位；排序逻辑放在 ProductList 里按映射排序
          }
          case 'boxhero_asc': {
            return 0; // 同上
          }
          case 'id_desc':
            return b.id - a.id;
          case 'id_asc':
            return a.id - b.id;
          default:
            return 0;
        }
      });
    }

    return filteredProducts;
  }, [productGroups]);

  // 搜索产品 - 简化版本，不使用分页
  const searchProducts = useCallback(async (
    searchFilters: ProductFilters,
    groupId?: number
  ) => {
    console.log('开始搜索产品:', { searchFilters, groupId });

    // 如果没有搜索条件和产品组，返回空结果
    if (!searchFilters.search && !groupId) {
      setState({
        products: [],
        isLoading: false,
        error: null,
        totalCount: 0,
      });
      return;
    }

    // 取消之前的请求
    cancelCurrentRequest();

    // 创建新的AbortController
    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    try {
      setState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('加载所有产品数据...');

      // 加载所有产品数据
      const response = await ApiService.fetchAllProducts();

      // 检查请求是否被取消
      if (abortController.signal.aborted) {
        return;
      }

      let productsData: Product[] = [];
      if (response.result) {
        if (Array.isArray(response.result)) {
          productsData = response.result;
        } else if (typeof response.result === 'object' && response.result !== null) {
          const result = response.result as any;
          if (result.data && Array.isArray(result.data)) {
            productsData = result.data;
          } else if (result.items && Array.isArray(result.items)) {
            productsData = result.items;
          }
        }
      }

      console.log('原始产品数据数量:', productsData.length);


      // 过滤和排序产品
      const filteredProducts = filterAndSortProducts(productsData, searchFilters, groupId);

      console.log('过滤后产品数量:', filteredProducts.length);

      // 添加产品组名称
      const productsWithGroupNames = filteredProducts.map((product: Product) => ({
        ...product,
        productGroupName: product.productGroupId ? productGroups[product.productGroupId] : undefined
      }));

      // 预加载库存信息
      if (productsWithGroupNames.length > 0) {
        const preloadIds = productsWithGroupNames.slice(0, 10).map(p => p.id);
        stockCache.preloadStock(preloadIds).catch(console.error);
      }

      setState({
        products: productsWithGroupNames,
        isLoading: false,
        error: null,
        totalCount: filteredProducts.length,
      });

    } catch (error) {
      if (abortController.signal.aborted) {
        return; // 忽略被取消的请求
      }

      console.error('搜索产品失败:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : '搜索失败',
      }));
    }
  }, [pageSize, productGroups, cancelCurrentRequest]);

  // 防抖搜索
  const debouncedSearch = useCallback((
    searchFilters: ProductFilters,
    groupId?: number,
    immediate: boolean = false
  ) => {
    console.log('防抖搜索被调用:', { searchFilters, groupId, immediate });

    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    if (immediate) {
      searchProducts(searchFilters, groupId);
    } else {
      debounceTimerRef.current = setTimeout(() => {
        console.log('防抖延迟后执行搜索');
        searchProducts(searchFilters, groupId);
      }, debounceMs);
    }
  }, [searchProducts, debounceMs]);

  // 不再需要loadMore函数，因为我们显示所有数据
  const loadMore = useCallback(() => {
    console.log('loadMore被调用，但已禁用分页功能');
  }, []);

  // 重置搜索
  const reset = useCallback(() => {
    cancelCurrentRequest();
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    setState({
      products: [],
      isLoading: false,
      error: null,
      totalCount: 0,
    });
  }, [cancelCurrentRequest]);

  // 刷新当前搜索
  const refresh = useCallback(() => {
    if (filters.search || selectedProductGroupId) {
      debouncedSearch(filters, selectedProductGroupId, true);
    } else {
      reset();
    }
  }, [filters, selectedProductGroupId, debouncedSearch, reset]);

  // 监听过滤条件变化
  useEffect(() => {
    const currentSearchParams = JSON.stringify({ filters, selectedProductGroupId });

    console.log('过滤条件变化检查:', {
      filters,
      selectedProductGroupId,
      lastParams: lastSearchParamsRef.current,
      currentParams: currentSearchParams,
      changed: lastSearchParamsRef.current !== currentSearchParams
    });

    // 只有当搜索参数真正变化时才重新搜索
    if (lastSearchParamsRef.current !== currentSearchParams) {
      lastSearchParamsRef.current = currentSearchParams;

      if (filters.search || selectedProductGroupId) {
        console.log('触发新搜索');
        debouncedSearch(filters, selectedProductGroupId);
      } else {
        console.log('重置搜索');
        reset();
      }
    } else {
      console.log('搜索参数未变化，跳过');
    }
  }, [filters, selectedProductGroupId, debouncedSearch, reset]);

  // 清理函数
  useEffect(() => {
    return () => {
      cancelCurrentRequest();
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [cancelCurrentRequest]);

  return {
    products: state.products,
    isLoading: state.isLoading,
    error: state.error,
    totalCount: state.totalCount,
    hasMore: false, // 总是false，因为我们显示所有数据
    isLoadingMore: false, // 总是false，因为没有分页
    loadMore, // 保留但不执行任何操作
    refresh,
    reset,
  };
}
