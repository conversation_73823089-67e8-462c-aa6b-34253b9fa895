export const runtime = 'nodejs';

import { NextRequest } from 'next/server';
import { getQuantitiesByBarcodes, getQuantitiesBySkus } from '@/dbClient';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json().catch(() => ({}));
    const barcodes: string[] = Array.isArray(body?.barcodes) ? body.barcodes : [];
    const skus: string[] = Array.isArray(body?.skus) ? body.skus : [];

    const [barcodeMap, skuMap] = await Promise.all([
      barcodes.length ? getQuantitiesByBarcodes(barcodes) : Promise.resolve({}),
      skus.length ? getQuantitiesBySkus(skus) : Promise.resolve({}),
    ]);

    const data = { ...skuMap, ...barcodeMap };

    return new Response(JSON.stringify({ ok: true, data }), {
      status: 200,
      headers: { 'content-type': 'application/json' },
    });
  } catch (e: any) {
    console.error('获取 BoxHero 库存失败:', e);
    return new Response(JSON.stringify({ ok: false, error: e?.message || 'internal error' }), {
      status: 500,
      headers: { 'content-type': 'application/json' },
    });
  }
}

