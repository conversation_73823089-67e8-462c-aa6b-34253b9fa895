---
type: "manual"
description: "Example description"
---
需求说明（Markdown）
markdown
Copy
Edit
# BoxHero → PostgreSQL 自动同步项目

## 项目目标
实现一个 Node.js 应用，每小时自动从 BoxHero API 获取库存数据，并同步到 PostgreSQL 数据库，仅保存 `name`、`barcode`、`quantity`。

---

## 功能需求

### 1. API 数据获取
- API 文档：https://rest.boxhero-app.com/docs/api#/
- API Token: `93dac409-c794-40a5-a388-c4283002f8a5`
- Endpoint: `GET /v1/items`
- 查询参数：
  - `limit=100`
  - `cursor` 分页获取数据
- 分页逻辑：
  - 从 `cursor=0` 开始
  - 每次获取 `limit=100`
  - 当返回的数据为空时结束
- 速率限制：
  - 每秒最多 5 次请求
  - 如果超出，等待 `X-Ratelimit-Reset` 秒后继续

---

### 2. 数据存储
- 数据表结构：
  ```sql
  CREATE TABLE IF NOT EXISTS boxhero_items (
      id SERIAL PRIMARY KEY,
      name TEXT,
      barcode TEXT UNIQUE,
      quantity INT,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
同步规则：

如果 barcode 已存在 → 更新记录

如果不存在 → 插入新记录

3. 自动化任务
使用 node-cron 每小时执行一次同步。

