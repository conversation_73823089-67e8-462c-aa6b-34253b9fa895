import { fetchAllItems } from './apiClient.js';
import { initSchema, upsertItems } from './dbClient.js';

export async function runSync() {
  const start = Date.now();
  await initSchema();

  const locationIds = (process.env.BOXHERO_LOCATION_IDS || '')
    .split(',')
    .map((s) => s.trim())
    .filter(Boolean);

  const items = await fetchAllItems({ locationIds });
  const filtered = items
    .filter((r) => r.barcode) // 只处理有 barcode 的
    .map((r) => ({
      name: r.name ?? null,
      barcode: String(r.barcode),
      quantity: Number(r.quantity ?? 0),
      boxhero_id: r.id ?? null,
      sku: r.sku ?? null,
    }));

  const { inserted, updated } = await upsertItems(filtered);

  const ms = Date.now() - start;
  console.log(`同步完成：插入 ${inserted} 条，更新 ${updated} 条，总计 ${filtered.length} 条，耗时 ${ms} ms`);
  return { inserted, updated, total: filtered.length, ms };
}

