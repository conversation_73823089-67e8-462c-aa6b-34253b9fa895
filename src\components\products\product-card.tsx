'use client';

import { useState, useEffect } from 'react';
import { Product, StockUpdate, StockInfo } from '@/lib/types';
import { ApiService } from '@/lib/api';
import { stockCache } from '@/lib/stock-cache';
import { StockControl } from './stock-control';
import { AdvancedProductEditModal } from './advanced-product-edit-modal';
import { formatPrice, getStockColor } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface ProductCardProps {
  product: Product;
  onUpdate: () => void;
}

type BoxheroMap = Record<string, number>;
export function ProductCard({ product, onUpdate, boxheroMap }: ProductCardProps & { boxheroMap?: BoxheroMap }) {
  const [stock, setStock] = useState<string | number>('加载中...');
  const [isUpdatingStock, setIsUpdatingStock] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isQuickEditing, setIsQuickEditing] = useState(false);
  const [quickEditName, setQuickEditName] = useState(product.name);
  const [quickEditPrice, setQuickEditPrice] = useState(product.price.toString());
  const [quickEditCost, setQuickEditCost] = useState((product.cost || 0).toString());
  const [quickEditBarcode, setQuickEditBarcode] = useState(product.barcode || '');

  // SKU 独立编辑状态
  const [sku, setSku] = useState<string>(product.sku || '');
  const [isEditingSku, setIsEditingSku] = useState(false);
  const [skuInput, setSkuInput] = useState<string>(sku);

  const [isSyncingBoxHero, setIsSyncingBoxHero] = useState(false);
  const [syncMessage, setSyncMessage] = useState<string | null>(null);


  // Stock input states
  const [stockInputValue, setStockInputValue] = useState('');

  useEffect(() => {
    loadStock();
  }, [product.id]);

  const loadStock = async () => {
    try {
      const stock = await stockCache.getStock(product.id);
      setStock(stock);
    } catch (error) {
      setStock('错误');
    }
  };

  // BoxHero库存：移除逐卡请求。数量在父组件批量加载并透传时可读取（保留字段用于显示）。
  // 从父组件透传 map 计算本卡片的 BoxHero 库存
  const boxHeroStock: number = Number(
    (boxheroMap && (boxheroMap[product.barcode || ''] ?? boxheroMap[sku || ''])) || 0
  );

  const handleStockUpdate = async (update: StockUpdate) => {
    try {
      setIsUpdatingStock(true);
      await ApiService.updateProductStock(update);
      // 清除该产品的库存缓存
      stockCache.clearProductCache(product.id);
      await loadStock();
      // 移除 onUpdate() 调用，避免页面刷新
      // onUpdate();
    } catch (error) {
      console.error('更新库存失败:', error);
    } finally {
      setIsUpdatingStock(false);
    }
  };

  const handleQuickSave = async () => {
    try {
      const updatedProduct: Product = {
        ...product,
        name: quickEditName,
        price: parseFloat(quickEditPrice),
        cost: parseFloat(quickEditCost) || 0,
        barcode: quickEditBarcode,
      };

      // 仅更新远端允许的字段（不含 SKU）
      await ApiService.updateProduct(updatedProduct);
      setIsQuickEditing(false);
      onUpdate();
    } catch (error) {
      console.error('快速编辑失败:', error);
      // 重置为原始值
      setQuickEditName(product.name);
      setQuickEditPrice(product.price.toString());
      setQuickEditCost((product.cost || 0).toString());
      setQuickEditBarcode(product.barcode || '');
    }
  };

  const handleQuickCancel = () => {
    setQuickEditName(product.name);
    setQuickEditPrice(product.price.toString());
    setQuickEditCost((product.cost || 0).toString());
    setQuickEditBarcode(product.barcode || '');
    setIsQuickEditing(false);
  };

  const handleStockAdd = async () => {
    if (!stockInputValue || isNaN(Number(stockInputValue)) || Number(stockInputValue) <= 0) return;

    setIsUpdatingStock(true);
    try {
      await handleStockUpdate({
        id: product.id,
        amount: Number(stockInputValue),
        type: 0 // 增加库存
      });
      setStockInputValue('');
    } catch (error) {
      console.error('增加库存失败:', error);
    } finally {
      setIsUpdatingStock(false);
    }
  };

  const handleStockRemove = async () => {
    if (!stockInputValue || isNaN(Number(stockInputValue)) || Number(stockInputValue) <= 0) return;

    setIsUpdatingStock(true);
    try {
      await handleStockUpdate({
        id: product.id,
        amount: Number(stockInputValue),
        type: 1 // 减少库存
      });
      setStockInputValue('');
    } catch (error) {
      console.error('减少库存失败:', error);
    } finally {
      setIsUpdatingStock(false);
    }
  };

  const getStockDisplay = () => {
    if (typeof stock === 'string') {
      return <span className="text-gray-600">{stock}</span>;
    }

    return <span className={`font-semibold ${getStockColor(stock)}`}>{stock}</span>;
  };

  return (
    <div className="bg-white rounded-lg border border-blue-200 shadow-sm hover:shadow-md transition-shadow duration-200">
      {/* 产品卡片主体 */}
      <div className="p-4">
        <div className="flex items-center justify-between">
          {/* 左侧：产品图标和信息 */}
          <div className="flex items-center space-x-4 flex-1">
            {/* 产品图标 */}
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <span className="text-orange-600 text-lg">📦</span>
            </div>

            {/* 产品信息 */}
            <div className="flex-1 min-w-0">
              {isQuickEditing ? (
                <div className="space-y-2">
                  <Input
                    value={quickEditName}
                    onChange={(e) => setQuickEditName(e.target.value)}
                    className="font-semibold text-gray-900"
                  />
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={quickEditPrice}
                      onChange={(e) => setQuickEditPrice(e.target.value)}
                      placeholder="价格"
                      className="text-sm"
                    />
                    <Input
                      value={quickEditBarcode}
                      onChange={(e) => setQuickEditBarcode(e.target.value)}
                      placeholder="条码"
                      className="text-sm"
                    />
                      {/* 条码右侧：SKU 就地编辑 */}
                      <span className="inline-flex items-center ml-2">
                        <span className="text-gray-400 mx-1">/</span>
                        {isEditingSku ? (
                          <>
                            <Input
                              value={skuInput}
                              onChange={(e) => setSkuInput(e.target.value)}
                              placeholder="SKU"
                              className="w-28 h-7 text-xs py-0.5"
                            />
                            <Button
                              variant="primary"
                              size="sm"
                              className="ml-1 px-2 py-0.5 text-xs"
                              onClick={async () => {
                                try {
                                  await fetch(`/api/local/products/${product.id}/sku`, {
                                    method: 'PUT',
                                    headers: { 'content-type': 'application/json' },
                                    body: JSON.stringify({ sku: skuInput })
                                  });
                                  setSku(skuInput);
                                  setIsEditingSku(false);
                                  onUpdate();
                                } catch (e) { console.error('更新 SKU 失败', e); }
                              }}
                            >保存</Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="ml-1 px-2 py-0.5 text-xs"
                              onClick={() => { setIsEditingSku(false); setSkuInput(sku); }}
                            >取消</Button>
                          </>
                        ) : (
                          <button
                            className="px-2 py-0.5 border rounded hover:bg-gray-50 text-gray-800"
                            title="点击修改SKU"
                            onClick={() => { setIsEditingSku(true); setSkuInput(sku); }}
                          >{sku || '-'}</button>
                        )}
                      </span>

                  </div>
                </div>
              ) : (
                <div>
                  <h3 className="font-semibold text-gray-900 truncate">
                    {product.name}
                  </h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                    <span>RM{product.price.toFixed(2)}</span>
                    {product.barcode && (
                      <>
                        <span>/ {product.barcode}</span>
                        <span className="inline-flex items-center ml-2">
                          <span className="text-gray-400 mx-1">/</span>
                          {isEditingSku ? (
                            <>
                              <Input
                                value={skuInput}
                                onChange={(e) => setSkuInput(e.target.value)}
                                placeholder="SKU"
                                className="w-28 h-6 text-xs py-0.5"
                              />
                              <Button
                                variant="primary"
                                size="sm"
                                className="ml-1 px-2 py-0.5 text-xs"
                                onClick={async () => {
                                  try {
                                    await fetch(`/api/local/products/${product.id}/sku`, {
                                      method: 'PUT',
                                      headers: { 'content-type': 'application/json' },
                                      body: JSON.stringify({ sku: skuInput })
                                    });
                                    setSku(skuInput);
                                    setIsEditingSku(false);
                                    onUpdate();
                                  } catch (e) { console.error('更新 SKU 失败', e); }
                                }}
                              >保存</Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="ml-1 px-2 py-0.5 text-xs"
                                onClick={() => { setIsEditingSku(false); setSkuInput(sku); }}
                              >取消</Button>
                            </>
                          ) : (
                            <button
                              className="px-2 py-0.5 border rounded hover:bg-gray-50 text-gray-800"
                              title="点击修改SKU"
                              onClick={() => { setIsEditingSku(true); setSkuInput(sku); }}
                            >{sku || '-'}</button>
                          )}
                        </span>
                      </>
                    )}
                    {product.productGroupName && (
                      <span>/ {product.productGroupName}</span>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 右侧：库存数量 */}
          <div className="flex items-center space-x-4 ml-4">
            {/* 系统库存 */}
            <div className="text-right">
              <div className="text-lg font-bold text-orange-600">
                {typeof stock === 'number' ? stock : '?'}
              </div>
              <div className="text-xs text-gray-500 uppercase">
                系统库存
              </div>
            </div>

            {/* BoxHero库存 */}
            <div className="text-right">
              <div className={`text-lg font-bold ${boxHeroStock > 0 ? 'text-green-600' : 'text-gray-400'}`}>
                {boxHeroStock}
              </div>
              <div className="text-xs text-gray-500 uppercase">
                BoxHero
              </div>
              {!product.barcode && (
                <div className="text-xs text-red-500">
                  无条码
                </div>
              )}
            </div>

            {/* 库存控制和操作按钮 */}
            <div className="flex items-center space-x-2">
              {/* 库存输入框和加减按钮 */}
              <div className="flex items-center space-x-1">
                <Input
                  type="number"
                  value={stockInputValue}
                  onChange={(e) => setStockInputValue(e.target.value)}
                  placeholder="数量"
                  className="w-16 text-xs"
                  disabled={isUpdatingStock}
                  min="1"
                />
                <Button
                  onClick={handleStockAdd}
                  disabled={isUpdatingStock || !stockInputValue || isNaN(Number(stockInputValue)) || Number(stockInputValue) <= 0}
                  variant="primary"
                  size="sm"
                  className="px-2 py-1 text-xs bg-green-600 hover:bg-green-700"
                >
                  +
                </Button>
                <Button
                  onClick={handleStockRemove}
                  disabled={isUpdatingStock || !stockInputValue || isNaN(Number(stockInputValue)) || Number(stockInputValue) <= 0}
                  variant="danger"
                  size="sm"
                  className="px-2 py-1 text-xs"
                >
                  -
                </Button>
              </div>

              {/* 操作按钮 */}
              {/* 同步 BoxHero 按钮 */}
              <Button
                variant="outline"
                size="sm"
                disabled={isSyncingBoxHero}
                onClick={async () => {
                  try {
                    setIsSyncingBoxHero(true);
                    setSyncMessage('正在同步 BoxHero 库存...');
                    const res = await fetch('/api/boxhero/sync', { method: 'POST' });
                    if (!res.ok) throw new Error(`HTTP ${res.status}`);
                    // 同步完成后刷新该产品的 BoxHero 数量（条码或SKU）
                    const payload: any = {};
                    if (product.barcode) payload.barcodes = [product.barcode];
                    if (product.sku) payload.skus = [product.sku];
                    if (payload.barcodes || payload.skus) {
                      await fetch('/api/boxhero/quantities', {
                        method: 'POST',
                        headers: { 'content-type': 'application/json' },
                        body: JSON.stringify(payload),
                      });
                      // 刷新成功，这里不更新本地数值，等待父组件下一次批量刷新（可触发上层 reload）
                    }
                    setSyncMessage('同步成功');
                    setTimeout(() => setSyncMessage(null), 2000);
                  } catch (e) {
                    console.error('手动同步失败:', e);
                    setSyncMessage('同步失败');
                    setTimeout(() => setSyncMessage(null), 2500);
                  } finally {
                    setIsSyncingBoxHero(false);
                  }
                }}
                className="px-3 py-1 text-xs"
              >
                {isSyncingBoxHero ? '同步中...' : '同步 BoxHero 库存'}
              </Button>
              {syncMessage && (
                <span className="text-xs text-gray-500 ml-2">{syncMessage}</span>
              )}

              <div className="flex space-x-2">
                {isQuickEditing ? (
                  <>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={handleQuickSave}
                      className="px-3 py-1 text-xs"
                    >
                      保存
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleQuickCancel}
                      className="px-3 py-1 text-xs"
                    >
                      取消
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => setIsQuickEditing(true)}
                      className="px-3 py-1 text-xs"
                    >
                      快速修改
                    </Button>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => setIsEditModalOpen(true)}
                      className="px-3 py-1 text-xs"
                    >
                      高级编辑
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>


      </div>

      {/* 高级编辑模态框 */}
      <AdvancedProductEditModal
        product={product}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onUpdate={onUpdate}
      />
    </div>
  );
}