import { supabaseServer } from '@/lib/supabase-server';

export async function initSchema() {
  // Supabase 托管数据库，通常通过迁移/SQL 脚本初始化。此处不再在代码里创建表，保持空实现。
}

export async function upsertItems(rows) {
  if (!rows?.length) return { inserted: 0, updated: 0 };
  const chunks = (a, size) => a.reduce((acc, _, i) => (i % size ? acc : [...acc, a.slice(i, i + size)]), []);
  let inserted = 0, updated = 0;
  for (const batch of chunks(rows, 500)) {
    const { data, error } = await supabaseServer
      .from('items')
      .upsert(batch.map(r => ({
        barcode: r.barcode ?? null,
        name: r.name ?? null,
        quantity: Number(r.quantity ?? 0),
        boxhero_id: r.boxhero_id ?? null,
        sku: r.sku ?? null,
        updated_at: new Date().toISOString(),
      })), { onConflict: 'barcode' })
      .select('barcode');
    if (error) throw error;
    // Supabase 无法直接区分插入/更新，简化为全部计入 updated
    updated += data?.length || 0;
  }
  return { inserted, updated };
}

export async function getQuantitiesByBarcodes(barcodes) {
  const list = Array.from(new Set((barcodes || []).filter(Boolean).map(String)));
  if (!list.length) return {};
  const { data, error } = await supabaseServer
    .from('items')
    .select('barcode, quantity')
    .in('barcode', list);
  if (error) throw error;
  const map = {};
  for (const row of data || []) map[row.barcode] = Number(row.quantity ?? 0);
  return map;
}

export async function getLinkedBarcodesByProductIds(productIds) {
  const ids = Array.from(new Set((productIds || []).filter((n) => Number.isFinite(n)).map(Number)));
  if (!ids.length) return {};
  const { data, error } = await supabaseServer
    .from('product_boxhero_links')
    .select('product_id, barcode')
    .in('product_id', ids);
  if (error) throw error;
  const map = {};
  for (const row of data || []) if (row.barcode) map[row.product_id] = row.barcode;
  return map;
}

export async function getQuantitiesBySkus(skus) {
  const list = Array.from(new Set((skus || []).filter(Boolean).map(String)));
  if (!list.length) return {};
  const { data, error } = await supabaseServer
    .from('items')
    .select('sku, barcode, quantity')
    .in('sku', list);
  if (error) throw error;
  const map = {};
  for (const row of data || []) {
    const key = row.sku || row.barcode;
    if (key) map[key] = Number(row.quantity ?? 0);
  }
  return map;
}

export async function upsertProductBoxheroLink({ product_id, barcode, boxhero_id, sku }) {
  if (!product_id) throw new Error('product_id is required');
  const payload = {
    product_id: Number(product_id),
    barcode: barcode ?? null,
    boxhero_id: boxhero_id ?? null,
    sku: sku ?? null,
    updated_at: new Date().toISOString(),
  };
  const { error } = await supabaseServer
    .from('product_boxhero_links')
    .upsert(payload, { onConflict: 'product_id' });
  if (error) throw error;
  return { ok: true };
}

export async function searchBoxheroItems({ search, limit = 50 }) {
  let query = supabaseServer
    .from('items')
    .select('boxhero_id, name, barcode, sku, quantity')
    .order('updated_at', { ascending: false })
    .limit(limit);
  if (search) {
    query = query.or(`name.ilike.%${search}%,barcode.ilike.%${search}%,sku.ilike.%${search}%`);
  }
  const { data, error } = await query;
  if (error) throw error;
  return data || [];
}
// ====== Local products (productbase) helpers ======
export async function getTableColumns(tableName) {
  if (String(tableName).toLowerCase() === 'productbase') {
    // 使用已知 schema，避免 information_schema 权限问题
    return [
      { column_name: 'productid', data_type: 'bigint' },
      { column_name: 'productgroupid', data_type: 'bigint' },
      { column_name: 'name', data_type: 'text' },
      { column_name: 'description', data_type: 'text' },
      { column_name: 'price', data_type: 'numeric' },
      { column_name: 'cost', data_type: 'numeric' },
      { column_name: 'points', data_type: 'numeric' },
      { column_name: 'pointsprice', data_type: 'numeric' },
      { column_name: 'barcode', data_type: 'text' },
      { column_name: 'purchaseoptions', data_type: 'integer' },
      { column_name: 'stockalert', data_type: 'boolean' },
      { column_name: 'stockproductid', data_type: 'bigint' },
      { column_name: 'stockproductamount', data_type: 'numeric' },
      { column_name: 'isdeleted', data_type: 'boolean' },
      { column_name: 'displayorder', data_type: 'integer' },
      { column_name: 'guid', data_type: 'text' },
      { column_name: 'sku', data_type: 'text' },
    ];
  }
  // 其他表：返回空数组（或后续按需补充特定表的静态定义）
  return [];
}

function pick(obj, keys) {
  const out = {};
  for (const k of keys) if (k in obj) out[k] = obj[k];
  return out;
}

function firstExistingKey(obj, keys, fallback = undefined) {
  for (const k of keys) if (k in obj && obj[k] != null) return obj[k];
  return fallback;
}

export function mapRowToProduct(row) {
  return {
    id: Number(firstExistingKey(row, ['productid', 'id', 'product_id', 'pid'], 0)),
    name: firstExistingKey(row, ['name', 'product_name'], ''),
    description: firstExistingKey(row, ['description'], undefined),
    barcode: firstExistingKey(row, ['barcode', 'bar_code'], undefined),
    sku: firstExistingKey(row, ['sku'], undefined),
    price: Number(firstExistingKey(row, ['price'], 0)),
    cost: Number(firstExistingKey(row, ['cost'], 0)) || undefined,
    points: Number(firstExistingKey(row, ['points'], undefined)) || undefined,
    pointsPrice: Number(firstExistingKey(row, ['pointsprice'], undefined)) || undefined,
    purchaseOptions: Number(firstExistingKey(row, ['purchaseoptions'], undefined)) || undefined,
    productGroupId: Number(firstExistingKey(row, ['productgroupid', 'product_group_id', 'group_id'], undefined)) || undefined,
    productGroupName: firstExistingKey(row, ['product_group_name', 'group_name'], undefined),
    stockTargetProductId: Number(firstExistingKey(row, ['stockproductid'], undefined)) || undefined,
    stockProductAmount: Number(firstExistingKey(row, ['stockproductamount'], undefined)) || undefined,
    stockAlert: Boolean(firstExistingKey(row, ['stockalert'], undefined)),
    displayOrder: Number(firstExistingKey(row, ['displayorder'], undefined)) || undefined,
    isDeleted: Boolean(firstExistingKey(row, ['isdeleted', 'is_deleted'], false)) || undefined,
    guid: firstExistingKey(row, ['guid'], undefined),
  };
}

export async function queryLocalProducts(params = {}) {
  // 使用静态列集合，避免 information_schema 权限
  const colSet = new Set(['productid','productgroupid','name','description','price','cost','points','pointsprice','barcode','purchaseoptions','stockalert','stockproductid','stockproductamount','isdeleted','displayorder','guid','sku']);

  let query = supabaseServer.from('productbase').select('*');

  if (params.search) {
    const likeable = ['name','barcode','description','sku'].filter(c => colSet.has(c));
    if (likeable.length) {
      // Supabase or() 组合 ILIKE 条件
      const orExpr = likeable.map(c => `${c}.ilike.%${params.search}%`).join(',');
      query = query.or(orExpr);
    }
  }

  if (params.productGroupId) {
    const gcol = ['productgroupid','product_group_id','group_id'].find(c => colSet.has(c));
    if (gcol) query = query.eq(gcol, Number(params.productGroupId));
  }

  // 排序
  const sort = params.sortBy;
  if (sort) {
    if (sort === 'name_asc' && colSet.has('name')) query = query.order('name', { ascending: true });
    else if (sort === 'name_desc' && colSet.has('name')) query = query.order('name', { ascending: false });
    else if (sort === 'price_desc' && colSet.has('price')) query = query.order('price', { ascending: false });
    else if (sort === 'price_asc' && colSet.has('price')) query = query.order('price', { ascending: true });
  }

  const limit = Math.max(1, Number(params.limit || 20));
  const offset = Math.max(0, Number(params.offset || 0));
  query = query.range(offset, offset + limit - 1);

  const { data, error } = await query;
  if (error) throw error;
  return (data || []).map(mapRowToProduct);
}

export async function countLocalProducts(params = {}) {
  // 使用静态列集合，避免 information_schema 权限
  const colSet = new Set(['productid','productgroupid','name','description','price','cost','points','pointsprice','barcode','purchaseoptions','stockalert','stockproductid','stockproductamount','isdeleted','displayorder','guid','sku']);

  let query = supabaseServer.from('productbase').select('*', { count: 'exact', head: true });

  if (params.search) {
    const likeable = ['name','barcode','description','sku'].filter(c => colSet.has(c));
    if (likeable.length) {
      const orExpr = likeable.map(c => `${c}.ilike.%${params.search}%`).join(',');
      query = query.or(orExpr);
    }
  }

  if (params.productGroupId) {
    const gcol = ['productgroupid','product_group_id','group_id'].find(c => colSet.has(c));
    if (gcol) query = query.eq(gcol, Number(params.productGroupId));
  }

  const { count, error } = await query;
  if (error) throw error;
  return count || 0;
}

export async function getLocalProductById(id) {
  const { data, error } = await supabaseServer
    .from('productbase')
    .select('*')
    .eq('productid', Number(id))
    .limit(1)
    .maybeSingle();
  if (error) throw error;
  return data ? mapRowToProduct(data) : null;
}


export async function closePool() {
  // Supabase 无连接池可关闭，此处留空
}
