import pg from 'pg';
const { Pool } = pg;

const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: Number(process.env.PGPORT || 5432),
  database: process.env.PGDATABASE || 'boxhero',
  user: process.env.PGUSER || 'postgres',
  password: process.env.PGPASSWORD || 'postgres',
  min: Number(process.env.PGPOOL_MIN || 0),
  max: Number(process.env.PGPOOL_MAX || 10),
  idleTimeoutMillis: Number(process.env.PGPOOL_IDLE || 10000),
});

export async function initSchema() {
  const client = await pool.connect();
  try {
    await client.query(`
      CREATE TABLE IF NOT EXISTS items (
        barcode TEXT PRIMARY KEY,
        name TEXT,
        quantity NUMERIC NOT NULL DEFAULT 0,
        boxhero_id TEXT,
        sku TEXT,
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
      );
      -- 兼容老表，补充缺失列
      DO $$ BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns WHERE table_name='items' AND column_name='boxhero_id'
        ) THEN
          ALTER TABLE items ADD COLUMN boxhero_id TEXT;
        END IF;
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns WHERE table_name='items' AND column_name='sku'
        ) THEN
          ALTER TABLE items ADD COLUMN sku TEXT;
        END IF;
      END $$;
    `);
    // 同步建立链接表
    await client.query(`
      CREATE TABLE IF NOT EXISTS product_boxhero_links (
        product_id INTEGER PRIMARY KEY,
        barcode TEXT,
        boxhero_id TEXT,
        sku TEXT,
        updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
        created_at TIMESTAMPTZ NOT NULL DEFAULT now()
      );
    `);
  } finally {
    client.release();
  }
}

export async function upsertItems(rows) {
  if (!rows?.length) return { inserted: 0, updated: 0 };
  const client = await pool.connect();
  try {
    const values = [];
    const placeholders = [];
    let i = 1;
    for (const r of rows) {
      placeholders.push(`($${i++}, $${i++}, $${i++}, $${i++}, $${i++})`);
      values.push(
        r.barcode ?? null,
        r.name ?? null,
        Number(r.quantity ?? 0),
        r.boxhero_id ?? null,
        r.sku ?? null
      );
    }
    const sql = `
      INSERT INTO items (barcode, name, quantity, boxhero_id, sku)
      VALUES ${placeholders.join(',')}
      ON CONFLICT (barcode)
      DO UPDATE SET
        name = EXCLUDED.name,
        quantity = EXCLUDED.quantity,
        boxhero_id = COALESCE(EXCLUDED.boxhero_id, items.boxhero_id),
        sku = COALESCE(EXCLUDED.sku, items.sku),
        updated_at = now()
      RETURNING (xmax = 0) AS inserted
    `;
    const res = await client.query(sql, values);
    let inserted = 0, updated = 0;
    for (const row of res.rows) {
      if (row.inserted) inserted++; else updated++;
    }
    return { inserted, updated };
  } finally {
    client.release();
  }
}

export async function getQuantitiesByBarcodes(barcodes) {
  const list = Array.from(new Set((barcodes || []).filter(Boolean).map(String)));
  if (!list.length) return {};
  const client = await pool.connect();
  try {
    const sql = 'SELECT barcode, quantity FROM items WHERE barcode = ANY($1::text[])';
    const res = await client.query(sql, [list]);
    const map = {};
    for (const row of res.rows) {
      map[row.barcode] = Number(row.quantity ?? 0);
    }
    return map;
  } finally {
    client.release();
  }
}

export async function getLinkedBarcodesByProductIds(productIds) {
  const ids = Array.from(new Set((productIds || []).filter((n) => Number.isFinite(n)).map(Number)));
  if (!ids.length) return {};
  const client = await pool.connect();
  try {
    const res = await client.query(
      'SELECT product_id, barcode FROM product_boxhero_links WHERE product_id = ANY($1::int[])',
      [ids]
    );
    const map = {};
    for (const row of res.rows) {
      if (row.barcode) map[row.product_id] = row.barcode;
    }
    return map;
  } finally {
    client.release();
  }
}

export async function upsertProductBoxheroLink({ product_id, barcode, boxhero_id, sku }) {
  if (!product_id) throw new Error('product_id is required');
  const client = await pool.connect();
  try {
    const sql = `
      INSERT INTO product_boxhero_links (product_id, barcode, boxhero_id, sku, updated_at)
      VALUES ($1, $2, $3, $4, now())
      ON CONFLICT (product_id)
      DO UPDATE SET
        barcode = COALESCE(EXCLUDED.barcode, product_boxhero_links.barcode),
        boxhero_id = COALESCE(EXCLUDED.boxhero_id, product_boxhero_links.boxhero_id),
        sku = COALESCE(EXCLUDED.sku, product_boxhero_links.sku),
        updated_at = now()
    `;
    await client.query(sql, [Number(product_id), barcode ?? null, boxhero_id ?? null, sku ?? null]);
    return { ok: true };
  } finally {
    client.release();
  }
}

export async function searchBoxheroItems({ search, limit = 50 }) {
  const client = await pool.connect();
  try {
    let where = '';
    const params = [];
    if (search) {
      params.push(`%${search}%`);
      where = `WHERE name ILIKE $1 OR barcode ILIKE $1 OR sku ILIKE $1`;
    }
    params.push(limit);
    const res = await client.query(
      `SELECT boxhero_id, name, barcode, sku, quantity FROM items ${where} ORDER BY updated_at DESC LIMIT $${params.length}`,
      params
    );
    return res.rows;
  } finally {
    client.release();
  }
}

export async function closePool() {
  await pool.end();
}
