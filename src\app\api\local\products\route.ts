export const runtime = 'nodejs';
import { NextRequest } from 'next/server';
import { queryLocalProducts, countLocalProducts } from '@/dbClient';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const search = searchParams.get('search') || undefined;
    const productGroupId = searchParams.get('productGroupId') ? Number(searchParams.get('productGroupId')) : undefined;
    const limit = searchParams.get('limit') ? Number(searchParams.get('limit')) : 20;
    const offset = searchParams.get('offset') ? Number(searchParams.get('offset')) : 0;
    const sortBy = searchParams.get('sortBy') || undefined;
    const inStockOnly = searchParams.get('inStockOnly') === 'true' ? true : undefined;

    const [items, count] = await Promise.all([
      queryLocalProducts({ search, productGroupId, limit, offset, sortBy, inStockOnly }),
      countLocalProducts({ search, productGroupId, inStockOnly })
    ]);

    return new Response(JSON.stringify({ ok: true, items, count }), { status: 200, headers: { 'content-type': 'application/json' } });
  } catch (e: any) {
    console.error('本地产品查询失败:', e);
    return new Response(JSON.stringify({ ok: false, error: e?.message || 'internal error' }), { status: 500, headers: { 'content-type': 'application/json' } });
  }
}

