export const runtime = 'nodejs';

import { NextRequest } from 'next/server';
import { runSync } from '@/syncService.js';

export async function POST(_req: NextRequest) {
  try {
    const result = await runSync();
    return new Response(JSON.stringify({ ok: true, result }), {
      status: 200,
      headers: { 'content-type': 'application/json' },
    });
  } catch (e: any) {
    console.error('手动同步失败:', e);
    return new Response(JSON.stringify({ ok: false, error: e?.message || 'internal error' }), {
      status: 500,
      headers: { 'content-type': 'application/json' },
    });
  }
}

