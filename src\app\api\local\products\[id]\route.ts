export const runtime = 'nodejs';
import { NextRequest } from 'next/server';
import { getLocalProductById } from '@/dbClient';

export async function GET(_req: NextRequest, context: { params: { id: string } }) {
  try {
    const id = Number(context.params.id);
    if (!id || Number.isNaN(id)) {
      return new Response(JSON.stringify({ ok: false, error: 'invalid id' }), { status: 400 });
    }
    const item = await getLocalProductById(id);
    if (!item) {
      return new Response(JSON.stringify({ ok: false, error: 'not found' }), { status: 404 });
    }
    return new Response(JSON.stringify({ ok: true, item }), { status: 200, headers: { 'content-type': 'application/json' } });
  } catch (e: any) {
    console.error('读取本地产品失败:', e);
    return new Response(JSON.stringify({ ok: false, error: e?.message || 'internal error' }), { status: 500, headers: { 'content-type': 'application/json' } });
  }
}

